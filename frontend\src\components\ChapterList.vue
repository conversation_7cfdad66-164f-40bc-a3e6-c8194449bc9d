<template>
  <div class="panel-content">
    <div class="panel-header">
      <div class="panel-title">
        <span class="panel-icon">📝</span>
        <span>章节列表</span>
      </div>
      <el-dropdown @command="handleChapterCommand">
        <el-button size="small" type="primary">
          <el-icon><Plus /></el-icon>
          新增章节 <el-icon><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="manual">手动创建</el-dropdown-item>
            <slot name="ai-commands">
              <!-- AI相关命令插槽，由父组件提供 -->
            </slot>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="chapters-list" v-loading="loading" element-loading-text="正在加载章节...">
      <div
          v-for="(chapter, index) in chapters"
          :key="chapter.id"
          class="chapter-item"
          :class="{ active: currentChapter?.id === chapter.id }"
          @click="selectChapter(chapter)"
      >
        <div class="chapter-info">
          <h4>第{{ index + 1 }}章</h4>
          <p>{{ chapter.title }}</p>
          <div class="chapter-meta">
            <span>{{ chapter.wordCount || 0 }}字</span>
            <el-tag v-if="chapter.status" :type="getChapterStatusType(chapter.status)" size="small">
              {{ getChapterStatusText(chapter.status) }}
            </el-tag>
          </div>
          <el-tooltip
              v-if="chapter.description"
              :content="chapter.description"
              placement="top-start"
              :disabled="chapter.description.length <= 50"
              effect="light"
              :show-after="300"
          >
            <p class="chapter-desc chapter-desc-truncated">
              {{ chapter.description.length > 50 ? chapter.description.substring(0, 50) + '...' : chapter.description }}
            </p>
          </el-tooltip>
        </div>
        <div class="chapter-actions">
          <el-dropdown @command="(cmd) => handleChapterAction(cmd, chapter)">
            <el-button size="small" type="text">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                <slot name="chapter-actions" :chapter="chapter">
                  <!-- 章节操作插槽，由父组件提供 -->
                </slot>
                <el-dropdown-item divided command="delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="chapters.length === 0" class="empty-chapters">
        <p>暂无章节</p>
        <el-button size="small" type="primary" @click="addNewChapter">
          创建第一章
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { Plus, ArrowDown, MoreFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  chapters: {
    type: Array,
    default: () => []
  },
  currentChapter: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'select-chapter',
  'add-chapter', 
  'edit-chapter',
  'delete-chapter',
  'chapter-command'
])

// 章节状态类型映射
const getChapterStatusType = (status) => {
  const statusMap = {
    draft: 'warning',
    completed: 'success',
    published: 'primary'
  }
  return statusMap[status] || 'warning'
}

// 章节状态文本映射
const getChapterStatusText = (status) => {
  const statusMap = {
    draft: '草稿',
    completed: '完成',
    published: '发表'
  }
  return statusMap[status] || '草稿'
}

// 选择章节
const selectChapter = (chapter) => {
  emit('select-chapter', chapter)
}

// 新增章节
const addNewChapter = () => {
  emit('add-chapter')
}

// 处理章节命令
const handleChapterCommand = (command) => {
  emit('chapter-command', command)
}

// 处理章节操作
const handleChapterAction = (command, chapter) => {
  switch (command) {
    case 'edit':
      emit('edit-chapter', chapter)
      break
    case 'delete':
      emit('delete-chapter', chapter)
      break
    default:
      // 其他命令通过事件传递给父组件
      emit('chapter-command', command, chapter)
      break
  }
}
</script>

<style scoped>
.panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.panel-icon {
  font-size: 16px;
}

.chapters-list {
  flex: 1;
  max-height: calc(100vh - 190px);
  overflow-y: auto;
  padding: 8px;
}

.chapter-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
}

.chapter-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.chapter-item.active {
  border-color: #409eff;
  background: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.chapter-info {
  flex: 1;
  min-width: 0;
}

.chapter-info h4 {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
  font-weight: normal;
}

.chapter-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.chapter-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.chapter-meta span {
  font-size: 12px;
  color: #909399;
}

.chapter-desc {
  margin: 0;
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.chapter-desc-truncated {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chapter-actions {
  margin-left: 8px;
  flex-shrink: 0;
}

.empty-chapters {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-chapters p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

/* 滚动条样式 */
.chapters-list::-webkit-scrollbar {
  width: 6px;
}

.chapters-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chapters-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chapters-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
